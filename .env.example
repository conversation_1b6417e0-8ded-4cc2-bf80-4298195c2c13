# Environment Variables Template
# Copy this file to .env and fill in your actual values

# Database Configuration
DB_SERVER=your_database_server_ip
DB_DATABASE=your_database_name
DB_USERNAME=your_database_username
DB_PASSWORD=your_database_password
DB_DRIVER={ODBC Driver 11 for SQL Server}

# Email Configuration
EMAIL_SENDER=<EMAIL>
EMAIL_PASSWORD=your_email_app_password
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587

# Default Email Recipients
EMAIL_RECEIVER_DEFAULT=<EMAIL>
EMAIL_FAILURE_RECIPIENT=<EMAIL>

# Application Configuration
CONFIG_ALARMS_FILE=./config/Alarmes List Norme RDS-PP_Tarec.xlsx
CONFIG_MANUAL_ADJUSTMENTS_FILE=./config/manual_adjustments.json
BASE_DATA_PATH=./monthly_data/data

# Security Notes:
# - Never commit the actual .env file to version control
# - Use app-specific passwords for email (not your regular password)
# - Ensure database user has only necessary permissions
# - Keep this file updated when adding new environment variables
